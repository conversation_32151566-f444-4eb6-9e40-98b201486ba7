#!/usr/bin/env python3
"""
聊天室客户端启动脚本

使用方法:
    python start_client.py [host] [port]

参数:
    host: 服务器地址 (默认: localhost)
    port: 服务器端口 (默认: 8888)

示例:
    python start_client.py
    python start_client.py ************* 9999
"""

import sys
import argparse
from client import ChatClient


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="多人在线聊天室客户端",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
  %(prog)s                    # 连接到默认服务器 (localhost:8888)
  %(prog)s *************      # 连接到指定服务器
  %(prog)s ************* 9999 # 连接到指定服务器和端口
        """
    )
    
    parser.add_argument(
        "host",
        nargs="?",
        default="localhost",
        help="服务器地址 (默认: localhost)"
    )
    
    parser.add_argument(
        "port",
        nargs="?",
        type=int,
        default=8888,
        help="服务器端口 (默认: 8888)"
    )
    
    parser.add_argument(
        "--host",
        dest="host_flag",
        help="服务器地址"
    )
    
    parser.add_argument(
        "--port",
        dest="port_flag",
        type=int,
        help="服务器端口"
    )
    
    parser.add_argument(
        "--version",
        action="version",
        version="聊天室客户端 v1.0.0"
    )
    
    args = parser.parse_args()
    
    # 处理命名参数优先级
    if args.host_flag:
        args.host = args.host_flag
    if args.port_flag:
        args.port = args.port_flag
    
    return args


def validate_arguments(args):
    """验证参数有效性"""
    # 验证端口范围
    if not (1 <= args.port <= 65535):
        print(f"❌ 错误: 端口号必须在 1-65535 范围内，当前值: {args.port}")
        sys.exit(1)
    
    # 验证主机地址格式（简单验证）
    if not args.host:
        print("❌ 错误: 服务器地址不能为空")
        sys.exit(1)


def print_client_info(host, port):
    """打印客户端信息"""
    print("=" * 60)
    print("💬 多人在线聊天室客户端")
    print("=" * 60)
    print(f"🎯 目标服务器: {host}:{port}")
    print("=" * 60)
    print("💡 使用提示:")
    print("  - 首次使用请先注册账户: /register <用户名> <密码>")
    print("  - 注册后登录: /login <用户名> <密码>")
    print("  - 输入 /help 查看所有命令")
    print("  - 按 Ctrl+C 或输入 /quit 退出")
    print("=" * 60)
    print()


def main():
    """主函数"""
    try:
        # 解析命令行参数
        args = parse_arguments()
        
        # 验证参数
        validate_arguments(args)
        
        # 打印客户端信息
        print_client_info(args.host, args.port)
        
        # 创建并启动客户端
        client = ChatClient(args.host, args.port)
        client.start()
        
    except KeyboardInterrupt:
        print("\n" + "=" * 60)
        print("👋 正在退出客户端...")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ 客户端启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
