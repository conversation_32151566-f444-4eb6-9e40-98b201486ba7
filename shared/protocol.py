"""
聊天室协议定义模块

定义了客户端和服务器之间通信的协议格式和消息类型。
协议采用Header-Payload结构，Header固定8字节，Payload为JSON格式并经过AES加密。
"""

import struct
import json
from enum import IntEnum
from typing import Dict, Any, Tuple, Optional


class MessageType(IntEnum):
    """消息类型枚举"""

    REGISTER_REQUEST = 0x01  # 注册请求
    REGISTER_RESPONSE = 0x02  # 注册响应
    LOGIN_REQUEST = 0x03  # 登录请求
    LOGIN_RESPONSE = 0x04  # 登录响应
    CHAT_MESSAGE = 0x05  # 聊天消息
    BROADCAST_MESSAGE = 0x06  # 广播消息
    HISTORY_REQUEST = 0x07  # 历史消息请求
    HISTORY_RESPONSE = 0x08  # 历史消息响应
    ERROR_NOTIFICATION = 0x09  # 错误通知
    HEARTBEAT = 0x0A  # 心跳包
    USER_LIST_UPDATE = 0x0B  # 在线用户列表更新
    USER_LIST_REQUEST = 0x0C  # 请求用户列表
    LOGOUT_REQUEST = 0x0D  # 用户主动退出请求


class Protocol:
    """协议处理类"""

    VERSION = 1  # 协议版本
    HEADER_SIZE = 8  # Header固定8字节

    def __init__(self, encryption_handler):
        """初始化协议处理器"""
        self.encryption_handler = encryption_handler
        self.message_counter = 0

    def pack_message(self, msg_type: MessageType, payload: Dict[str, Any]) -> bytes:
        """打包消息为二进制格式"""
        # 将payload转换为JSON字符串
        json_payload = json.dumps(payload, ensure_ascii=False)

        # 加密payload
        encrypted_payload = self.encryption_handler.encrypt(json_payload)

        # 生成消息序号
        self.message_counter = (self.message_counter + 1) % 65536

        # 构建Header (8字节)
        # 格式: 版本(1) + 类型(1) + 序号(2) + 长度(4)
        header = struct.pack(
            "!BBHI",
            self.VERSION,  # 协议版本 (1 byte)
            msg_type,  # 消息类型 (1 byte)
            self.message_counter,  # 消息序号 (2 bytes, network order)
            len(encrypted_payload),
        )  # Payload长度 (4 bytes, network order)

        return header + encrypted_payload

    def unpack_message(
        self, data: bytes
    ) -> Optional[Tuple[MessageType, Dict[str, Any], int]]:
        """解包二进制消息"""
        if len(data) < self.HEADER_SIZE:
            return None

        try:
            # 解析Header
            version, msg_type, seq_num, payload_length = struct.unpack(
                "!BBHI", data[: self.HEADER_SIZE]
            )

            # 检查协议版本
            if version != self.VERSION:
                raise ValueError(f"不支持的协议版本: {version}")

            # 检查消息类型
            if msg_type not in MessageType:
                raise ValueError(f"未知的消息类型: {msg_type}")

            # 检查数据长度
            if len(data) < self.HEADER_SIZE + payload_length:
                return None  # 数据不完整

            # 提取加密的payload
            encrypted_payload = data[
                self.HEADER_SIZE : self.HEADER_SIZE + payload_length
            ]

            # 解密payload
            decrypted_payload = self.encryption_handler.decrypt(encrypted_payload)

            # 解析JSON
            payload = json.loads(decrypted_payload)

            return MessageType(msg_type), payload, seq_num

        except (struct.error, ValueError, json.JSONDecodeError) as e:
            print(f"消息解包错误: {e}")
            return None

    def create_register_request(self, username: str, password_hash: str) -> bytes:
        """创建注册请求消息"""
        payload = {"username": username, "password_hash": password_hash}
        return self.pack_message(MessageType.REGISTER_REQUEST, payload)

    def create_register_response(self, success: bool, message: str) -> bytes:
        """创建注册响应消息"""
        payload = {"success": success, "message": message}
        return self.pack_message(MessageType.REGISTER_RESPONSE, payload)

    def create_login_request(self, username: str, password_hash: str) -> bytes:
        """创建登录请求消息"""
        payload = {"username": username, "password_hash": password_hash}
        return self.pack_message(MessageType.LOGIN_REQUEST, payload)

    def create_login_response(
        self, success: bool, message: str, username: str = None
    ) -> bytes:
        """创建登录响应消息"""
        payload = {"success": success, "message": message}
        if username:
            payload["username"] = username
        return self.pack_message(MessageType.LOGIN_RESPONSE, payload)

    def create_chat_message(self, username: str, message: str) -> bytes:
        """创建聊天消息"""
        payload = {
            "username": username,
            "message": message,
            "timestamp": None,  # 服务器端会添加时间戳
        }
        return self.pack_message(MessageType.CHAT_MESSAGE, payload)

    def create_broadcast_message(
        self, username: str, message: str, timestamp: str
    ) -> bytes:
        """创建广播消息"""
        payload = {"username": username, "message": message, "timestamp": timestamp}
        return self.pack_message(MessageType.BROADCAST_MESSAGE, payload)

    def create_history_request(self, count: int = 10) -> bytes:
        """创建历史消息请求"""
        payload = {"count": count}
        return self.pack_message(MessageType.HISTORY_REQUEST, payload)

    def create_history_response(self, messages: list) -> bytes:
        """创建历史消息响应"""
        payload = {"messages": messages}
        return self.pack_message(MessageType.HISTORY_RESPONSE, payload)

    def create_error_notification(self, error_code: str, message: str) -> bytes:
        """创建错误通知消息"""
        payload = {"error_code": error_code, "message": message}
        return self.pack_message(MessageType.ERROR_NOTIFICATION, payload)

    def create_heartbeat(self) -> bytes:
        """创建心跳包"""
        payload = {"timestamp": None}  # 发送时会添加时间戳
        return self.pack_message(MessageType.HEARTBEAT, payload)

    def create_user_list_request(self) -> bytes:
        """创建用户列表请求"""
        payload = {}
        return self.pack_message(MessageType.USER_LIST_REQUEST, payload)

    def create_user_list_update(self, online_users: list) -> bytes:
        """创建用户列表更新消息"""
        payload = {"online_users": online_users}
        return self.pack_message(MessageType.USER_LIST_UPDATE, payload)

    def create_logout_request(self) -> bytes:
        """创建用户退出请求"""
        payload = {}
        return self.pack_message(MessageType.LOGOUT_REQUEST, payload)


def get_message_size(header_data: bytes) -> Optional[int]:
    """从Header数据中获取完整消息的大小"""
    if len(header_data) < Protocol.HEADER_SIZE:
        return None

    try:
        # 解析Header中的payload长度
        _, _, _, payload_length = struct.unpack(
            "!BBHI", header_data[: Protocol.HEADER_SIZE]
        )
        return Protocol.HEADER_SIZE + payload_length
    except struct.error:
        return None
