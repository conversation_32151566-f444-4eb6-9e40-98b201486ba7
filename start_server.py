#!/usr/bin/env python3
"""
聊天室服务器启动脚本

使用方法:
    python start_server.py [host] [port]

参数:
    host: 服务器监听地址 (默认: localhost)
    port: 服务器监听端口 (默认: 8888)

示例:
    python start_server.py
    python start_server.py 0.0.0.0 9999
"""

import sys
import argparse
from server import ChatServer


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="多人在线聊天室服务器",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
  %(prog)s                    # 使用默认设置启动 (localhost:8888)
  %(prog)s --host 0.0.0.0     # 监听所有网络接口
  %(prog)s --port 9999        # 使用端口 9999
  %(prog)s ************* 8080 # 监听指定IP和端口
        """
    )
    
    parser.add_argument(
        "host",
        nargs="?",
        default="localhost",
        help="服务器监听地址 (默认: localhost)"
    )
    
    parser.add_argument(
        "port",
        nargs="?",
        type=int,
        default=8888,
        help="服务器监听端口 (默认: 8888)"
    )
    
    parser.add_argument(
        "--host",
        dest="host_flag",
        help="服务器监听地址"
    )
    
    parser.add_argument(
        "--port",
        dest="port_flag",
        type=int,
        help="服务器监听端口"
    )
    
    parser.add_argument(
        "--version",
        action="version",
        version="聊天室服务器 v1.0.0"
    )
    
    args = parser.parse_args()
    
    # 处理命名参数优先级
    if args.host_flag:
        args.host = args.host_flag
    if args.port_flag:
        args.port = args.port_flag
    
    return args


def validate_arguments(args):
    """验证参数有效性"""
    # 验证端口范围
    if not (1 <= args.port <= 65535):
        print(f"❌ 错误: 端口号必须在 1-65535 范围内，当前值: {args.port}")
        sys.exit(1)
    
    # 验证主机地址格式（简单验证）
    if not args.host:
        print("❌ 错误: 主机地址不能为空")
        sys.exit(1)


def print_server_info(host, port):
    """打印服务器信息"""
    print("=" * 60)
    print("🚀 多人在线聊天室服务器")
    print("=" * 60)
    print(f"📡 监听地址: {host}")
    print(f"🔌 监听端口: {port}")
    print(f"🔗 连接地址: {host}:{port}")
    print("=" * 60)
    print("💡 提示:")
    print("  - 按 Ctrl+C 停止服务器")
    print("  - 客户端可以使用以下命令连接:")
    print(f"    python start_client.py {host} {port}")
    print("=" * 60)
    print()


def main():
    """主函数"""
    try:
        # 解析命令行参数
        args = parse_arguments()
        
        # 验证参数
        validate_arguments(args)
        
        # 打印服务器信息
        print_server_info(args.host, args.port)
        
        # 创建并启动服务器
        server = ChatServer(args.host, args.port)
        server.start()
        
    except KeyboardInterrupt:
        print("\n" + "=" * 60)
        print("👋 收到中断信号，正在关闭服务器...")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ 服务器启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
